# DICOM MIP 视频批量处理器

这是一个用于批量处理DICOM数据生成最大强度投影(MIP)旋转视频的Python程序。

## 文件结构

- `mip_processor.py` - 核心处理函数库，包含DICOM加载、MIP渲染和视频生成功能
- `main.py` - 批量处理主程序，支持多进程并行处理
- `20250418-20250607/` - 示例DICOM数据目录

## 功能特性

- 🔍 自动扫描指定目录下的所有TOF 3D DICOM序列
- 🎥 为每个序列生成水平和垂直旋转的MIP视频
- ⚡ 支持多进程并行处理，充分利用CPU资源
- 📊 实时进度显示和处理统计
- 🎛️ 丰富的命令行参数配置
- 📁 智能输出目录组织

## 安装依赖

```bash
pip install numpy SimpleITK pyvista imageio
```

## 使用方法

### 1. 扫描模式（推荐先运行）

```bash
# 扫描所有DICOM文件，不实际处理
python main.py --dry-run
```

### 2. 基本使用

```bash
# 使用默认参数处理所有文件
python main.py

# 指定输入和输出目录
python main.py -i 20250418-20250607 -o my_output
```

### 3. 自定义参数

```bash
# 自定义视频参数
python main.py -s 15 -f 24 -r 1920x1080 -b 30M

# 限制并行进程数
python main.py -p 4

# 使用不同的编码器
python main.py -c libx264
```

## 命令行参数

| 参数 | 简写 | 默认值 | 说明 |
|------|------|--------|------|
| `--input-dir` | `-i` | `20250418-20250607` | 输入目录路径 |
| `--output-dir` | `-o` | `output_videos` | 输出目录路径 |
| `--processes` | `-p` | CPU核心数 | 并行进程数 |
| `--seconds` | `-s` | `10` | 视频时长（秒） |
| `--fps` | `-f` | `30` | 视频帧率 |
| `--resolution` | `-r` | `2560x1920` | 视频分辨率 |
| `--codec` | `-c` | `h264_videotoolbox` | 视频编码器 |
| `--bitrate` | `-b` | `50M` | 视频比特率 |
| `--dry-run` | - | - | 仅扫描文件，不处理 |

## 输出结构

```
output_videos/
├── 20250418/
│   ├── chen^nanfeng_20250418-175955-0674_180658_tof3d_tra_uCS_701/
│   │   ├── chen^nanfeng_20250418-175955-0674_180658_20250418_tof3d_tra_uCS_701_horizontal_spin.mp4
│   │   └── chen^nanfeng_20250418-175955-0674_180658_20250418_tof3d_tra_uCS_701_vertical_spin.mp4
│   └── liu^biao_20250418-141753-0673_141857_tof3d_tra_uCS_1801/
│       ├── liu^biao_20250418-141753-0673_141857_20250418_tof3d_tra_uCS_1801_horizontal_spin.mp4
│       └── liu^biao_20250418-141753-0673_141857_20250418_tof3d_tra_uCS_1801_vertical_spin.mp4
├── 20250421/
│   └── ...
└── ...
```

## 性能优化建议

1. **多进程数量**: 默认使用所有CPU核心，可根据内存情况调整
2. **视频质量**: 高分辨率和高比特率会显著增加处理时间
3. **存储空间**: 每个视频约50-200MB，请确保有足够磁盘空间
4. **内存使用**: 每个进程约需要2-4GB内存

## 示例输出

```
=== DICOM MIP 视频批量处理器 ===
输入目录: 20250418-20250607
输出目录: output_videos
并行进程数: 10
视频参数: 10秒, 30fps, 2560x1920, h264_videotoolbox, 50M

正在扫描DICOM文件...
找到 42 个DICOM序列
总计 7860 个DICOM文件

开始处理 42 个任务...
✓ 完成: chen^nanfeng_20250418-175955-0674_180658_20250418_tof3d_tra_uCS_701 (耗时: 45.2秒)
✓ 完成: liu^biao_20250418-141753-0673_141857_20250418_tof3d_tra_uCS_1801 (耗时: 32.1秒)
...

=== 处理完成 ===
总耗时: 1245.6秒
成功: 42
失败: 0
平均处理时间: 29.7秒/案例
```

## 故障排除

### 常见问题

1. **内存不足**: 减少并行进程数 (`-p 2`)
2. **编码器不支持**: 尝试使用 `libx264` 编码器
3. **DICOM文件损坏**: 检查失败案例列表
4. **磁盘空间不足**: 清理输出目录或使用更低的比特率

### 错误日志

程序会显示详细的错误信息，包括：
- 失败的案例列表
- 具体错误原因
- 处理统计信息

## 技术细节

### MIP渲染特性

- 等方重采样提高图像质量
- Gamma校正增强对比度
- GPU加速渲染（如果可用）
- 自适应透明度函数
- 抗锯齿处理

### 视频生成

- 水平360度旋转
- 垂直110度摆动（-80°到30°）
- 高质量H.264编码
- 可配置的分辨率和比特率

## 许可证

本项目仅供学习和研究使用。
