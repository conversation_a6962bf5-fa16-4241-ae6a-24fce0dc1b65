import os
import numpy as np
import SimpleITK as sitk
import pyvista as pv
import imageio.v2 as iio

def resample_isotropic(itk_img, interp=sitk.sitkLinear):
    """将原始各向异性体素重采样为等方，提高 MIP 清晰度与一致性。"""
    sp = itk_img.GetSpacing()          # (sx, sy, sz)
    new_sp = [min(sp)] * 3
    size = itk_img.GetSize()           # (nx, ny, nz) in ITK (x,y,z)
    new_sz = [int(round(sz * s / new_sp[i])) for i, (sz, s) in enumerate(zip(size, sp))]

    rs = sitk.ResampleImageFilter()
    rs.SetInterpolator(interp)
    rs.SetOutputSpacing(new_sp)
    rs.SetSize(new_sz)
    rs.SetOutputDirection(itk_img.GetDirection())
    rs.SetOutputOrigin(itk_img.GetOrigin())
    return rs.Execute(itk_img)


def load_dicom_series(series_dir: str):
    reader = sitk.ImageSeriesReader()
    series_ids = reader.GetGDCMSeriesIDs(series_dir)
    if not series_ids:
        raise RuntimeError(f"No DICOM series in: {series_dir}")

    file_names = reader.GetGDCMSeriesFileNames(series_dir, series_ids[0])
    reader.SetFileNames(file_names)
    img = reader.Execute()  # SimpleITK Image

    # 1) 等方重采样（大幅减少台阶/重影）
    img_iso = resample_isotropic(img)

    # 2) 对比度增强
    vol_zyx = sitk.GetArrayFromImage(img_iso).astype(np.float32)  # (z,y,x)
    print(f"原始强度范围: [{vol_zyx.min():.1f}, {vol_zyx.max():.1f}]")

    # 应用gamma校正增强对比度
    vol_contrast = vol_zyx.copy()
    vol_contrast = vol_contrast / vol_contrast.max()  # 归一化到[0,1]
    gamma = 0.5  # 小于1会增强亮区域
    vol_zyx = np.power(vol_contrast, gamma)

    print(f"Gamma校正后强度范围: [{vol_zyx.min():.3f}, {vol_zyx.max():.3f}]")

    spacing = img_iso.GetSpacing()   # (sx, sy, sz) 物理单位
    origin = img_iso.GetOrigin()

    # VTK 使用 (x,y,z) & Fortran 顺序
    vol_xyz = np.ascontiguousarray(np.transpose(vol_zyx, (2, 1, 0)))  # (x,y,z)
    nx, ny, nz = vol_xyz.shape

    grid = pv.ImageData()
    grid.dimensions = (nx, ny, nz)
    grid.spacing = spacing
    grid.origin = origin
    grid.point_data["scalars"] = vol_xyz.ravel(order="F").astype(np.float32)

    print(f"Spacing (iso): {spacing}, volume shape (z,y,x): {vol_zyx.shape}")
    return grid, vol_zyx  # grid 用于 GPU 渲染，vol_zyx 用于快速 MIP PNG


# ======================
# GPU MIP 旋转视频（PyVista/VTK）
# ======================
def make_horizontal_spin_video(
    grid: pv.ImageData,
    out_path: str = "tof3d_horizontal_spin.mp4",
    seconds: int = 10,
    fps: int = 30,
    window_size=(2560, 1920),
    codec: str = "h264_videotoolbox",
    bitrate: str = "50M"
):
    """生成水平转动视频"""
    n_frames = seconds * fps
    az_step = 360.0 / n_frames  # 水平转动步长

    pv.global_theme.smooth_shading = False
    pl = pv.Plotter(off_screen=True, window_size=window_size)
    pl.set_background("black")

    # 添加体并启用 MIP
    vol = pl.add_volume(
        grid,
        cmap="gray",
        shade=False,
        opacity="linear",
    )
    mapper = vol.mapper
    mapper.SetBlendModeToMaximumIntensity()

    # 简化透明度设置
    opacity_tf = vol.prop.GetScalarOpacity()
    opacity_tf.RemoveAllPoints()
    opacity_tf.AddPoint(0.0, 0.0)    # 背景透明
    opacity_tf.AddPoint(0.3, 0.1)    # 低强度区域微弱显示
    opacity_tf.AddPoint(0.7, 0.8)    # 血管区域清晰显示
    opacity_tf.AddPoint(1.0, 1.0)    # 最亮区域完全显示

    # 关键：采样距离跟体素间距走 + 抖动抗走样 + 自动步长
    sd = 0.2 * min(grid.spacing)  # 更小的采样距离，提高血管细节
    mapper.SetSampleDistance(sd)
    mapper.SetAutoAdjustSampleDistances(False)  # 禁用自动调整，保持一致性
    try:
        mapper.SetUseJittering(True)
    except Exception:
        pass
    try:
        mapper.SetUseFloatingPointFrameBuffer(True)
    except Exception:
        pass

    # 调整初始视角 - 平视角度，更近距离
    pl.view_isometric()

    # 设置相机为低角度仰视并拉近距离
    pl.camera.elevation = -22  # 低角度仰视（负值表示从下往上看）
    pl.camera.zoom(1.8)        # 拉近1.8倍距离

    # 打开视频写入
    pl.open_movie(
        out_path,
        framerate=fps,
        codec=codec,
        bitrate=bitrate,
        quality=None,
        ffmpeg_params=["-pix_fmt", "yuv420p"],
    )

    # 渲一帧初始化
    pl.show(auto_close=False)
    pl.render()
    pl.write_frame()

    # 水平环绕一周
    print(f"开始水平转动 ({n_frames} 帧，{seconds}秒)...")
    for i in range(n_frames - 1):
        pl.camera.azimuth += az_step
        pl.render()
        pl.write_frame()

    pl.close()
    print(f"水平转动视频保存: {os.path.abspath(out_path)}")


def make_vertical_spin_video(
    grid: pv.ImageData,
    out_path: str = "tof3d_vertical_spin.mp4",
    seconds: int = 10,
    fps: int = 30,
    window_size=(2560, 1920),
    codec: str = "h264_videotoolbox",
    bitrate: str = "50M"
):
    """生成垂直转动视频"""
    n_frames = seconds * fps

    pv.global_theme.smooth_shading = False
    pl = pv.Plotter(off_screen=True, window_size=window_size)
    pl.set_background("black")

    # 添加体并启用 MIP
    vol = pl.add_volume(
        grid,
        cmap="gray",
        shade=False,
        opacity="linear",
    )
    mapper = vol.mapper
    mapper.SetBlendModeToMaximumIntensity()

    # 简化透明度设置，参照analyze_tof.py的简洁方法
    opacity_tf = vol.prop.GetScalarOpacity()
    opacity_tf.RemoveAllPoints()
    opacity_tf.AddPoint(0.0, 0.0)    # 背景透明
    opacity_tf.AddPoint(0.3, 0.1)    # 低强度区域微弱显示
    opacity_tf.AddPoint(0.7, 0.8)    # 血管区域清晰显示
    opacity_tf.AddPoint(1.0, 1.0)    # 最亮区域完全显示

    # 关键：采样距离跟体素间距走 + 抖动抗走样 + 自动步长
    sd = 0.2 * min(grid.spacing)  # 更小的采样距离，提高血管细节
    mapper.SetSampleDistance(sd)
    mapper.SetAutoAdjustSampleDistances(False)  # 禁用自动调整，保持一致性
    try:
        mapper.SetUseJittering(True)
    except Exception:
        pass
    try:
        mapper.SetUseFloatingPointFrameBuffer(True)
    except Exception:
        pass

    # 调整初始视角 - 平视角度，更近距离
    pl.view_isometric()

    # 设置相机为平视角度（elevation=0）并拉近距离
    pl.camera.elevation = 0  # 平视角度
    pl.camera.zoom(1.8)      # 拉近2倍距离

    # 打开视频写入
    pl.open_movie(
        out_path,
        framerate=fps,
        codec=codec,
        bitrate=bitrate,
        quality=None,
        ffmpeg_params=["-pix_fmt", "yuv420p"],
    )

    # 渲一帧初始化
    pl.show(auto_close=False)
    pl.render()
    pl.write_frame()

    # 垂直转动 - 以平视为中心的角度范围
    print(f"开始垂直转动 ({n_frames} 帧，{seconds}秒)...")
    start_elevation = -80.0   # 从稍微仰视开始
    end_elevation = 30.0      # 到稍微俯视结束
    elevation_range = end_elevation - start_elevation  # 60度总范围，以平视为中心
    elevation_step = elevation_range / n_frames  # 每帧的角度步长

    # 设置初始角度
    pl.camera.elevation = start_elevation

    # 垂直转动（与水平转动逻辑一致）
    for i in range(n_frames - 1):
        pl.camera.elevation += elevation_step
        pl.render()
        pl.write_frame()

    print(f"垂直转动完成，从 {start_elevation}° 到 {end_elevation}°")

    pl.close()
    print(f"垂直转动视频保存: {os.path.abspath(out_path)}")



if __name__ == "__main__":
    DICOM_DIR = "./tof3d_tra_uCS_701"

    grid, vol_zyx = load_dicom_series(DICOM_DIR)
    # 生成水平转动视频
    make_horizontal_spin_video(
        grid,
        out_path="tof3d_horizontal_spin.mp4",
        seconds=10,
        fps=30,
        window_size=(2560, 1920),
        codec="h264_videotoolbox",
        bitrate="50M"
    )

    # 生成垂直转动视频
    make_vertical_spin_video(
        grid,
        out_path="tof3d_vertical_spin.mp4",
        seconds=10,
        fps=30,
        window_size=(2560, 1920),
        codec="h264_videotoolbox",
        bitrate="50M"
    )
