#!/usr/bin/env python3
"""
批量处理DICOM数据生成MIP视频的主程序
支持多进程并行处理
"""

import os
import glob
import time
from multiprocessing import cpu_count
from concurrent.futures import ProcessPoolExecutor, as_completed
import argparse
from mip_processor import process_dicom_series


def find_dicom_directories(root_dir: str):
    """
    在指定根目录下查找所有包含DICOM文件的目录

    Args:
        root_dir: 根目录路径

    Returns:
        list: 包含DICOM文件的目录路径列表，每个元素为(dicom_dir, patient_info)
    """
    dicom_dirs = []

    # 遍历所有日期文件夹
    for date_dir in sorted(glob.glob(os.path.join(root_dir, "202*"))):
        if not os.path.isdir(date_dir):
            continue

        print(f"扫描日期目录: {date_dir}")

        # 遍历每个日期下的患者文件夹
        for patient_dir in glob.glob(os.path.join(date_dir, "*")):
            if not os.path.isdir(patient_dir):
                continue

            # 查找tof3d相关的DICOM序列文件夹
            tof_dirs = glob.glob(os.path.join(patient_dir, "*tof3d*"))

            for tof_dir in tof_dirs:
                if not os.path.isdir(tof_dir):
                    continue

                # 检查是否包含DICOM文件
                dcm_files = glob.glob(os.path.join(tof_dir, "*.dcm"))
                if dcm_files:
                    # 提取患者信息
                    patient_name = os.path.basename(patient_dir)
                    date_str = os.path.basename(date_dir)
                    series_name = os.path.basename(tof_dir)

                    patient_info = {
                        'patient_name': patient_name,
                        'date': date_str,
                        'series': series_name,
                        'dcm_count': len(dcm_files)
                    }

                    dicom_dirs.append((tof_dir, patient_info))
                    print(f"  找到DICOM序列: {tof_dir} ({len(dcm_files)} 文件)")

    return dicom_dirs


def process_single_case(args):
    """
    处理单个病例的包装函数，用于多进程

    Args:
        args: (dicom_dir, patient_info, output_base_dir, video_params)

    Returns:
        dict: 处理结果
    """
    dicom_dir, patient_info, output_base_dir, video_params = args

    try:
        # 创建输出目录结构
        output_dir = os.path.join(
            output_base_dir,
            patient_info['date'],
            f"{patient_info['patient_name']}_{patient_info['series']}"
        )

        # 生成文件名前缀
        prefix = f"{patient_info['patient_name']}_{patient_info['date']}_{patient_info['series']}"

        start_time = time.time()

        # 处理DICOM序列
        horizontal_path, vertical_path = process_dicom_series(
            dicom_dir=dicom_dir,
            output_dir=output_dir,
            patient_name=prefix,
            **video_params
        )

        end_time = time.time()
        processing_time = end_time - start_time

        result = {
            'dicom_dir': dicom_dir,
            'patient_info': patient_info,
            'output_dir': output_dir,
            'horizontal_path': horizontal_path,
            'vertical_path': vertical_path,
            'processing_time': processing_time,
            'success': horizontal_path is not None and vertical_path is not None
        }

        if result['success']:
            print(f"✓ 完成: {prefix} (耗时: {processing_time:.1f}秒)")
        else:
            print(f"✗ 失败: {prefix}")

        return result

    except Exception as e:
        print(f"✗ 错误: {patient_info['patient_name']} - {str(e)}")
        return {
            'dicom_dir': dicom_dir,
            'patient_info': patient_info,
            'success': False,
            'error': str(e)
        }


def main():
    parser = argparse.ArgumentParser(description='批量处理DICOM数据生成MIP视频')
    parser.add_argument('--input-dir', '-i', default='20250418-20250607',
                       help='输入目录路径 (默认: 20250418-20250607)')
    parser.add_argument('--output-dir', '-o', default='output_videos',
                       help='输出目录路径 (默认: output_videos)')
    parser.add_argument('--processes', '-p', type=int, default=None,
                       help='并行进程数 (默认: CPU核心数)')
    parser.add_argument('--seconds', '-s', type=int, default=10,
                       help='视频时长秒数 (默认: 10)')
    parser.add_argument('--fps', '-f', type=int, default=30,
                       help='视频帧率 (默认: 30)')
    parser.add_argument('--resolution', '-r', default='2560x1920',
                       help='视频分辨率 (默认: 2560x1920)')
    parser.add_argument('--codec', '-c', default='h264_videotoolbox',
                       help='视频编码器 (默认: h264_videotoolbox)')
    parser.add_argument('--bitrate', '-b', default='50M',
                       help='视频比特率 (默认: 50M)')
    parser.add_argument('--dry-run', action='store_true',
                       help='仅扫描文件，不实际处理')

    args = parser.parse_args()

    # 解析分辨率
    try:
        width, height = map(int, args.resolution.split('x'))
        window_size = (width, height)
    except ValueError:
        print(f"错误: 无效的分辨率格式 '{args.resolution}'，应为 'WIDTHxHEIGHT'")
        return 1

    # 设置进程数
    if args.processes is None:
        num_processes = cpu_count()
    else:
        num_processes = max(1, min(args.processes, cpu_count()))

    print(f"=== DICOM MIP 视频批量处理器 ===")
    print(f"输入目录: {args.input_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"并行进程数: {num_processes}")
    print(f"视频参数: {args.seconds}秒, {args.fps}fps, {args.resolution}, {args.codec}, {args.bitrate}")
    print()

    # 查找所有DICOM目录
    print("正在扫描DICOM文件...")
    dicom_dirs = find_dicom_directories(args.input_dir)

    if not dicom_dirs:
        print("未找到任何DICOM文件!")
        return 1

    print(f"\n找到 {len(dicom_dirs)} 个DICOM序列")

    # 显示扫描结果
    total_files = sum(info['dcm_count'] for _, info in dicom_dirs)
    print(f"总计 {total_files} 个DICOM文件")

    if args.dry_run:
        print("\n=== 扫描结果 (--dry-run模式) ===")
        for _, info in dicom_dirs:
            print(f"  {info['date']} | {info['patient_name']} | {info['series']} | {info['dcm_count']} 文件")
        return 0

    # 准备处理参数
    video_params = {
        'seconds': args.seconds,
        'fps': args.fps,
        'window_size': window_size,
        'codec': args.codec,
        'bitrate': args.bitrate
    }

    # 准备任务列表
    tasks = [(dicom_dir, patient_info, args.output_dir, video_params)
             for dicom_dir, patient_info in dicom_dirs]

    print(f"\n开始处理 {len(tasks)} 个任务...")
    start_time = time.time()

    # 多进程处理
    results = []
    if num_processes == 1:
        # 单进程模式
        for task in tasks:
            result = process_single_case(task)
            results.append(result)
    else:
        # 多进程模式
        with ProcessPoolExecutor(max_workers=num_processes) as executor:
            future_to_task = {executor.submit(process_single_case, task): task for task in tasks}

            for future in as_completed(future_to_task):
                result = future.result()
                results.append(result)

    end_time = time.time()
    total_time = end_time - start_time

    # 统计结果
    successful = [r for r in results if r.get('success', False)]
    failed = [r for r in results if not r.get('success', False)]

    print(f"\n=== 处理完成 ===")
    print(f"总耗时: {total_time:.1f}秒")
    print(f"成功: {len(successful)}")
    print(f"失败: {len(failed)}")

    if successful:
        avg_time = sum(r.get('processing_time', 0) for r in successful) / len(successful)
        print(f"平均处理时间: {avg_time:.1f}秒/案例")

    if failed:
        print(f"\n失败的案例:")
        for result in failed:
            info = result['patient_info']
            error = result.get('error', '未知错误')
            print(f"  {info['patient_name']} ({info['date']}) - {error}")

    return 0 if not failed else 1


if __name__ == "__main__":
    exit(main())